@echo off
title Update Users Data - Student Management System
color 0A

echo.
echo ==========================================
echo      📊 DATA UPDATE UTILITY
echo ==========================================
echo.
echo 🔄 Updating user data...
echo 👤 Adding new users to the system
echo.

REM Check Java
java -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ ERROR: Java is not installed or not in PATH
    echo Please install Java and try again
    pause
    exit /b 1
)

echo 🔨 Compiling application...

REM Try to find Maven
set MVN_CMD=mvn
where mvn >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 🔍 Maven not in PATH, trying IntelliJ Maven...
    if exist "C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.3\plugins\maven\lib\maven3\bin\mvn.cmd" (
        set MVN_CMD="C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.3\plugins\maven\lib\maven3\bin\mvn.cmd"
    ) else if exist "C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.8.5-bin\5i5jha092a3i37g0paqnfr15e0\apache-maven-3.8.5\bin\mvn.cmd" (
        set MVN_CMD="C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.8.5-bin\5i5jha092a3i37g0paqnfr15e0\apache-maven-3.8.5\bin\mvn.cmd"
    )
)

call %MVN_CMD% clean compile -q

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Compilation failed!
    echo Trying alternative compilation...
    
    if not exist "target\classes" mkdir target\classes
    javac -cp "src/main/java" -d target/classes src/main/java/com/example/doancuoikyjava/util/DataUpdater.java
    
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Alternative compilation also failed!
        pause
        exit /b 1
    )
)

echo 🚀 Running Data Update...
echo.

REM Build classpath first
echo 📦 Building classpath...
call %MVN_CMD% dependency:build-classpath -Dmdep.outputFile=temp-cp.txt -q
if exist temp-cp.txt (
    set /p CP=<temp-cp.txt
    del temp-cp.txt
) else (
    echo ❌ Failed to build classpath!
    pause
    exit /b 1
)

REM Run the data updater
java -cp "target/classes;%CP%" com.example.doancuoikyjava.util.DataUpdater

echo.
echo 🎉 Data update completed!
echo.
echo 📋 You can now login with these accounts:
echo    👤 Admin: admin/admin123
echo    👨‍🏫 Teachers: teacher/teacher123, Hiền/Hien123, Thành/Thanh123
echo    🎓 Students: Quân/Quan123, Nam/Nam123, Cầm/Cam123, student/student123, Bảo/Bao123
echo.
pause
