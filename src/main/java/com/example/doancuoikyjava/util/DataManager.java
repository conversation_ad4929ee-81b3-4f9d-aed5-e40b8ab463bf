package com.example.doancuoikyjava.util;

import com.example.doancuoikyjava.model.*;
import java.io.*;
import java.time.LocalDate;
import java.util.*;

public class DataManager {
    private static final String DATA_DIR = "data/";
    private static final String USERS_FILE = DATA_DIR + "users.txt";
    private static final String COURSES_FILE = DATA_DIR + "courses.txt";
    private static final String GRADES_FILE = DATA_DIR + "grades.txt";

    static {
        createDataDirectory();
        initializeDefaultData();
    }

    private static void createDataDirectory() {
        File dir = new File(DATA_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    public static void initializeDefaultData() {
        if (!new File(USERS_FILE).exists()) {
            createDefaultUsers();
        }
        if (!new File(COURSES_FILE).exists()) {
            createDefaultCourses();
        }
    }

    private static void createDefaultUsers() {
        List<User> defaultUsers = new ArrayList<>();

        // ADMIN USERS
        Admin admin1 = new Admin("ADM001", "admin", "admin123", "Quản trị viên",
                "<EMAIL>", "0123456789", LocalDate.of(1980, 1, 1),
                "123 Đường ABC, TP.HCM", "ADM001", "IT", "FULL");
        defaultUsers.add(admin1);

        // TEACHER USERS
        Teacher teacher1 = new Teacher("GV001", "teacher", "teacher123", "Nguyễn Văn Giáo",
                "<EMAIL>", "0987654321", LocalDate.of(1985, 5, 15),
                "NULL", "GV001", "Khoa học máy tính", "Giảng viên",
                15000000, "Thạc sĩ", 5);
        defaultUsers.add(teacher1);

        Teacher teacher2 = new Teacher("TCH003", "Hiền", "Hien123", "Nguyễn Đức Hiền",
                "<EMAIL>", "0763538272", LocalDate.of(1982, 6, 22),
                "Huế", "TCH003", "Khoa học máy tính", "Giảng viên",
                18000000, "Tiến sĩ", 8);
        defaultUsers.add(teacher2);

        Teacher teacher3 = new Teacher("TCH004", "Thành", "Thanh123", "Nguyễn Thành",
                "thanh123", "0353905455", LocalDate.of(2025, 6, 9),
                "Đà Nẵng", "TCH004", "Khoa học máy tính", "Giảng viên",
                16000000, "Thạc sĩ", 3);
        defaultUsers.add(teacher3);

        // STUDENT USERS - Updated with your provided data
        Student student1 = new Student("STU001", "Quân", "Quan123", "Phạm Huynh Quân",
                "<EMAIL>", "0353905455", LocalDate.of(2005, 6, 18),
                "Đà Nẵng", "STU001", "24IT1", "Công nghệ thông tin", 1);
        student1.setGpa(3.5);
        defaultUsers.add(student1);

        Student student2 = new Student("STU002", "Nam", "Nam123", "Trần Nam",
                "<EMAIL>", "0372047377", LocalDate.of(2006, 6, 8),
                "Huế", "STU002", "24IT2", "Công nghệ thông tin", 1);
        student2.setGpa(3.2);
        defaultUsers.add(student2);

        Student student3 = new Student("STU003", "Cầm", "Cam123", "Đinh Hoàng Cầm",
                "<EMAIL>", "0763538272", LocalDate.of(2006, 6, 28),
                "Lâm Đồng", "STU003", "24IT3", "Công nghệ thông tin", 1);
        student3.setGpa(3.8);
        defaultUsers.add(student3);

        Student student4 = new Student("SV001", "student", "student123", "Nguyễn Văn Học",
                "<EMAIL>", "0123987654", LocalDate.of(2002, 5, 15),
                "123 Đường ABC, Quận 1, TP.HCM", "SV001", "24IT4", "Công nghệ thông tin", 2);
        student4.setGpa(3.0);
        defaultUsers.add(student4);

        Student student5 = new Student("GV002", "Bảo", "Bao123", "Lê Phú Bảo",
                "<EMAIL>", "0763538272", LocalDate.of(1997, 7, 1),
                "Đà Nẵng", "GV002", "24IT5", "Công nghệ thông tin", 3);
        student5.setGpa(2.9);
        defaultUsers.add(student5);

        saveUsers(defaultUsers);
    }

    /**
     * Phương thức để cập nhật/thêm người dùng mới vào hệ thống
     */
    public static void updateUsersData() {
        List<User> existingUsers = loadUsers();
        List<User> newUsers = new ArrayList<>();

        // Tạo danh sách người dùng mới cần thêm
        createUpdatedUsersList(newUsers);

        // Thêm những người dùng chưa tồn tại
        for (User newUser : newUsers) {
            boolean exists = existingUsers.stream()
                    .anyMatch(existing -> existing.getUserId().equals(newUser.getUserId()) ||
                             existing.getUsername().equals(newUser.getUsername()));

            if (!exists) {
                existingUsers.add(newUser);
                System.out.println("✅ Đã thêm người dùng: " + newUser.getFullName() + " (" + newUser.getUserId() + ")");
            } else {
                System.out.println("⚠️ Người dùng đã tồn tại: " + newUser.getFullName() + " (" + newUser.getUserId() + ")");
            }
        }

        saveUsers(existingUsers);
        System.out.println("🎉 Cập nhật dữ liệu người dùng hoàn tất!");
    }

    private static void createUpdatedUsersList(List<User> usersList) {
        // ADMIN USERS
        Admin admin1 = new Admin("ADM001", "admin", "admin123", "Quản trị viên",
                "<EMAIL>", "0123456789", LocalDate.of(1980, 1, 1),
                "123 Đường ABC, TP.HCM", "ADM001", "IT", "FULL");
        usersList.add(admin1);

        // TEACHER USERS
        Teacher teacher1 = new Teacher("GV001", "teacher", "teacher123", "Nguyễn Văn Giáo",
                "<EMAIL>", "0987654321", LocalDate.of(1985, 5, 15),
                "NULL", "GV001", "Khoa học máy tính", "Giảng viên",
                15000000, "Thạc sĩ", 5);
        usersList.add(teacher1);

        Teacher teacher2 = new Teacher("TCH003", "Hiền", "Hien123", "Nguyễn Đức Hiền",
                "<EMAIL>", "0763538272", LocalDate.of(1982, 6, 22),
                "Huế", "TCH003", "Khoa học máy tính", "Giảng viên",
                18000000, "Tiến sĩ", 8);
        usersList.add(teacher2);

        Teacher teacher3 = new Teacher("TCH004", "Thành", "Thanh123", "Nguyễn Thành",
                "<EMAIL>", "0353905455", LocalDate.of(2025, 6, 9),
                "Đà Nẵng", "TCH004", "Khoa học máy tính", "Giảng viên",
                16000000, "Thạc sĩ", 3);
        usersList.add(teacher3);

        // STUDENT USERS - Updated with your provided data
        Student student1 = new Student("STU001", "Quân", "Quan123", "Phạm Huynh Quân",
                "<EMAIL>", "0353905455", LocalDate.of(2005, 6, 18),
                "Đà Nẵng", "STU001", "24IT1", "Công nghệ thông tin", 1);
        student1.setGpa(3.5);
        usersList.add(student1);

        Student student2 = new Student("STU002", "Nam", "Nam123", "Trần Nam",
                "<EMAIL>", "0372047377", LocalDate.of(2006, 6, 8),
                "Huế", "STU002", "24IT2", "Công nghệ thông tin", 1);
        student2.setGpa(3.2);
        usersList.add(student2);

        Student student3 = new Student("STU003", "Cầm", "Cam123", "Đinh Hoàng Cầm",
                "<EMAIL>", "0763538272", LocalDate.of(2006, 6, 28),
                "Lâm Đồng", "STU003", "24IT3", "Công nghệ thông tin", 1);
        student3.setGpa(3.8);
        usersList.add(student3);

        Student student4 = new Student("SV001", "student", "student123", "Nguyễn Văn Học",
                "<EMAIL>", "0123987654", LocalDate.of(2002, 5, 15),
                "123 Đường ABC, Quận 1, TP.HCM", "SV001", "24IT4", "Công nghệ thông tin", 2);
        student4.setGpa(3.0);
        usersList.add(student4);

        Student student5 = new Student("GV002", "Bảo", "Bao123", "Lê Phú Bảo",
                "<EMAIL>", "0763538272", LocalDate.of(1997, 7, 1),
                "Đà Nẵng", "GV002", "24IT5", "Công nghệ thông tin", 3);
        student5.setGpa(2.9);
        usersList.add(student5);
    }

    private static void createDefaultCourses() {
        List<Course> defaultCourses = new ArrayList<>();

        Course course1 = new Course("CS101", "Lập trình Java", "Khóa học lập trình Java cơ bản", 3,
                "TCH001", "Thứ 2, 4, 6 - 7:30-9:30", "A101", 30);
        course1.setTeacherName("Nguyễn Văn A");
        course1.addStudent("STU001"); // Add default student to course

        Course course2 = new Course("CS102", "Cơ sở dữ liệu", "Khóa học về hệ quản trị cơ sở dữ liệu", 3,
                "TCH001", "Thứ 3, 5, 7 - 9:30-11:30", "A102", 25);
        course2.setTeacherName("Nguyễn Văn A");
        course2.addStudent("STU001"); // Add default student to course

        defaultCourses.add(course1);
        defaultCourses.add(course2);

        saveCourses(defaultCourses);

        // Create some default grades
        createDefaultGrades();
    }

    private static void createDefaultGrades() {
        // This will be handled by GradeService when needed
        // Just create the grades file structure
        File gradesFile = new File("data/grades.txt");
        if (!gradesFile.exists()) {
            try {
                gradesFile.createNewFile();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // User management methods
    public static List<User> loadUsers() {
        List<User> users = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(USERS_FILE))) {
            String line;
            while ((line = reader.readLine()) != null) {
                User user = parseUser(line);
                if (user != null) {
                    users.add(user);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return users;
    }

    public static void saveUsers(List<User> users) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(USERS_FILE))) {
            for (User user : users) {
                writer.println(userToString(user));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static User parseUser(String line) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }

        String[] parts = line.split("\\|");
        if (parts.length < 8) return null; // Minimum required fields

        // Handle old format without avatar field
        String role = parts.length > 8 ? parts[8] : "STUDENT"; // Default to STUDENT for old format
        User user = null;

        // Determine role-specific field positions based on format
        int avatarIndex = 9;
        int roleSpecificStart = 10;

        // Handle old format (without avatar field)
        if (parts.length < 10 && parts.length >= 8) {
            role = parts.length > 8 ? parts[8] : "STUDENT";
            avatarIndex = -1; // No avatar field
            roleSpecificStart = 9;
        }

        switch (role) {
            case "ADMIN":
                int adminRequiredFields = avatarIndex == -1 ? 12 : 13;
                if (parts.length >= adminRequiredFields) {
                    user = new Admin();
                    ((Admin) user).setAdminId(parts[roleSpecificStart]);
                    ((Admin) user).setDepartment(parts[roleSpecificStart + 1]);
                    ((Admin) user).setAccessLevel(parts[roleSpecificStart + 2]);
                }
                break;
            case "TEACHER":
                int teacherRequiredFields = avatarIndex == -1 ? 15 : 16;
                if (parts.length >= teacherRequiredFields) {
                    user = new Teacher();
                    ((Teacher) user).setTeacherId(parts[roleSpecificStart]);
                    ((Teacher) user).setDepartment(parts[roleSpecificStart + 1]);
                    ((Teacher) user).setPosition(parts[roleSpecificStart + 2]);
                    ((Teacher) user).setSalary(Double.parseDouble(parts[roleSpecificStart + 3]));
                    ((Teacher) user).setQualification(parts[roleSpecificStart + 4]);
                    ((Teacher) user).setExperienceYears(Integer.parseInt(parts[roleSpecificStart + 5]));
                }
                break;
            case "STUDENT":
                int studentRequiredFields = avatarIndex == -1 ? 13 : 14;
                if (parts.length >= studentRequiredFields) {
                    user = new Student();
                    ((Student) user).setStudentId(parts[roleSpecificStart]);
                    ((Student) user).setClassName(parts[roleSpecificStart + 1]);
                    ((Student) user).setMajor(parts[roleSpecificStart + 2]);
                    ((Student) user).setYear(Integer.parseInt(parts[roleSpecificStart + 3]));
                }
                break;
        }

        if (user != null) {
            user.setUserId(parts[0]);
            user.setUsername(parts[1]);
            user.setPassword(parts[2]);
            user.setFullName(parts[3]);
            user.setEmail(parts[4]);
            user.setPhone(parts[5]);

            // Safe date parsing
            try {
                if (parts[6] != null && !parts[6].equals("null") && !parts[6].trim().isEmpty()) {
                    user.setDateOfBirth(LocalDate.parse(parts[6]));
                } else {
                    user.setDateOfBirth(LocalDate.of(2000, 1, 1)); // Default date
                }
            } catch (Exception e) {
                System.err.println("⚠️ Error parsing date for user " + parts[0] + ": " + parts[6]);
                user.setDateOfBirth(LocalDate.of(2000, 1, 1)); // Default date
            }

            user.setAddress(parts[7]);

            // Set avatar path if exists (new field, might not exist in old data)
            if (avatarIndex != -1 && parts.length > avatarIndex) {
                String avatarPath = parts[avatarIndex];
                if (!avatarPath.equals("null") && !avatarPath.trim().isEmpty()) {
                    user.setAvatarPath(avatarPath);
                }
            }
        }

        return user;
    }

    private static String userToString(User user) {
        StringBuilder sb = new StringBuilder();
        sb.append(user.getUserId()).append("|")
          .append(user.getUsername()).append("|")
          .append(user.getPassword()).append("|")
          .append(user.getFullName()).append("|")
          .append(user.getEmail()).append("|")
          .append(user.getPhone()).append("|")
          .append(user.getDateOfBirth()).append("|")
          .append(user.getAddress()).append("|")
          .append(user.getRole()).append("|")
          .append(user.getAvatarPath() != null ? user.getAvatarPath() : "null").append("|");

        if (user instanceof Admin) {
            Admin admin = (Admin) user;
            sb.append(admin.getAdminId()).append("|")
              .append(admin.getDepartment()).append("|")
              .append(admin.getAccessLevel());
        } else if (user instanceof Teacher) {
            Teacher teacher = (Teacher) user;
            sb.append(teacher.getTeacherId()).append("|")
              .append(teacher.getDepartment()).append("|")
              .append(teacher.getPosition()).append("|")
              .append(teacher.getSalary()).append("|")
              .append(teacher.getQualification()).append("|")
              .append(teacher.getExperienceYears());
        } else if (user instanceof Student) {
            Student student = (Student) user;
            sb.append(student.getStudentId()).append("|")
              .append(student.getClassName()).append("|")
              .append(student.getMajor()).append("|")
              .append(student.getYear());
        }

        return sb.toString();
    }

    // Course management methods
    public static List<Course> loadCourses() {
        List<Course> courses = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(COURSES_FILE))) {
            String line;
            while ((line = reader.readLine()) != null) {
                Course course = parseCourse(line);
                if (course != null) {
                    courses.add(course);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return courses;
    }

    public static void saveCourses(List<Course> courses) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(COURSES_FILE))) {
            for (Course course : courses) {
                writer.println(courseToString(course));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static Course parseCourse(String line) {
        String[] parts = line.split("\\|");
        if (parts.length < 8) return null;

        Course course = new Course();
        course.setCourseId(parts[0]);
        course.setCourseName(parts[1]);
        course.setDescription(parts[2]);
        course.setCredits(Integer.parseInt(parts[3]));
        course.setTeacherId(parts[4]);
        course.setTeacherName(parts[5]);
        course.setSchedule(parts[6]);
        course.setClassroom(parts[7]);
        course.setMaxStudents(Integer.parseInt(parts[8]));

        if (parts.length > 9 && !parts[9].isEmpty()) {
            String[] students = parts[9].split(",");
            for (String student : students) {
                if (!student.trim().isEmpty()) {
                    course.getEnrolledStudents().add(student.trim());
                }
            }
        }

        return course;
    }

    private static String courseToString(Course course) {
        StringBuilder sb = new StringBuilder();
        sb.append(course.getCourseId()).append("|")
          .append(course.getCourseName()).append("|")
          .append(course.getDescription()).append("|")
          .append(course.getCredits()).append("|")
          .append(course.getTeacherId()).append("|")
          .append(course.getTeacherName()).append("|")
          .append(course.getSchedule()).append("|")
          .append(course.getClassroom()).append("|")
          .append(course.getMaxStudents()).append("|")
          .append(String.join(",", course.getEnrolledStudents()));

        return sb.toString();
    }
}
