package com.example.doancuoikyjava.util;

/**
 * Utility class để cập nhật dữ liệu người dùng
 */
public class DataUpdater {
    
    public static void main(String[] args) {
        System.out.println("🔄 Bắt đầu cập nhật dữ liệu người dùng...");
        
        try {
            // Cập nhật dữ liệu người dùng
            DataManager.updateUsersData();
            
            System.out.println("✅ Cập nhật dữ liệu thành công!");
            System.out.println("📋 Danh sách người dùng đã được cập nhật với:");
            System.out.println("   👤 Admin: admin/admin123");
            System.out.println("   👨‍🏫 Teachers: teacher/teacher123, Hiền/Hien123, Thành/Thanh123");
            System.out.println("   🎓 Students: Quân/Quan123, Nam/Nam123, <PERSON><PERSON><PERSON>/Cam123, student/student123, <PERSON>ả<PERSON>/Bao123");
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi khi cập nhật dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Phương thức để reset và tạo lại toàn bộ dữ liệu
     */
    public static void resetAndCreateData() {
        System.out.println("🔄 Reset và tạo lại toàn bộ dữ liệu...");
        
        try {
            // Xóa file dữ liệu cũ
            java.io.File usersFile = new java.io.File("data/users.txt");
            if (usersFile.exists()) {
                usersFile.delete();
                System.out.println("🗑️ Đã xóa dữ liệu cũ");
            }
            
            // Tạo lại dữ liệu mặc định
            DataManager.initializeDefaultData();
            
            System.out.println("✅ Tạo lại dữ liệu thành công!");
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi khi reset dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
