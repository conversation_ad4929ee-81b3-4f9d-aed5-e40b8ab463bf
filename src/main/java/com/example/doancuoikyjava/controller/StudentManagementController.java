package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class StudentManagementController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button addStudentBtn;
    @FXML private Button refreshBtn;
    @FXML private Button searchBtn;
    @FXML private Button exportBtn;
    @FXML private Button deleteSelectedBtn;
    
    @FXML private TextField searchField;
    @FXML private ComboBox<String> classFilterComboBox;
    @FXML private ComboBox<String> majorFilterComboBox;
    
    @FXML private TableView<Student> studentsTableView;
    @FXML private TableColumn<Student, String> studentIdColumn;
    @FXML private TableColumn<Student, String> fullNameColumn;
    @FXML private TableColumn<Student, String> classNameColumn;
    @FXML private TableColumn<Student, String> majorColumn;
    @FXML private TableColumn<Student, Integer> yearColumn;
    @FXML private TableColumn<Student, String> gpaColumn;
    @FXML private TableColumn<Student, String> emailColumn;
    @FXML private TableColumn<Student, String> phoneColumn;
    @FXML private TableColumn<Student, Void> actionsColumn;
    
    @FXML private Label totalStudentsLabel;
    
    private UserService userService;
    private ObservableList<Student> allStudents;
    private ObservableList<Student> filteredStudents;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            System.out.println("🔄 StudentManagementController: Bắt đầu khởi tạo...");

            userService = new UserService();
            allStudents = FXCollections.observableArrayList();
            filteredStudents = FXCollections.observableArrayList();

            System.out.println("✅ StudentManagementController: UserService đã được khởi tạo");

            setupWelcomeMessage();
            setupTableColumns();
            setupFilters();
            loadStudents();

            // Enable multiple selection
            studentsTableView.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);

            System.out.println("✅ StudentManagementController: Khởi tạo hoàn tất");
        } catch (Exception e) {
            System.err.println("❌ StudentManagementController: Lỗi khởi tạo: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi khởi tạo", "Không thể khởi tạo trang quản lý sinh viên: " + e.getMessage());
        }
    }
    
    private void setupWelcomeMessage() {
        try {
            User currentUser = SceneManager.getCurrentUser();
            if (currentUser != null) {
                welcomeLabel.setText("Xin chào, " + currentUser.getFullName());
                System.out.println("👤 StudentManagementController: Current user: " + currentUser.getFullName());
            } else {
                welcomeLabel.setText("Xin chào, Admin");
                System.out.println("⚠️ StudentManagementController: Current user is null, using default");
            }
        } catch (Exception e) {
            System.err.println("❌ StudentManagementController: Lỗi setup welcome message: " + e.getMessage());
            welcomeLabel.setText("Xin chào, Admin");
        }
    }
    
    private void setupTableColumns() {
        studentIdColumn.setCellValueFactory(new PropertyValueFactory<>("studentId"));
        fullNameColumn.setCellValueFactory(new PropertyValueFactory<>("fullName"));
        classNameColumn.setCellValueFactory(new PropertyValueFactory<>("className"));
        majorColumn.setCellValueFactory(new PropertyValueFactory<>("major"));
        yearColumn.setCellValueFactory(new PropertyValueFactory<>("year"));
        emailColumn.setCellValueFactory(new PropertyValueFactory<>("email"));
        phoneColumn.setCellValueFactory(new PropertyValueFactory<>("phone"));
        
        // GPA column with formatting
        gpaColumn.setCellValueFactory(cellData -> {
            double gpa = cellData.getValue().getGpa();
            return new SimpleStringProperty(String.format("%.2f", gpa));
        });
        
        // Actions column with buttons
        actionsColumn.setCellFactory(param -> new TableCell<Student, Void>() {
            private final Button editBtn = new Button("Sửa");
            private final Button deleteBtn = new Button("Xóa");
            private final HBox buttons = new HBox(5, editBtn, deleteBtn);
            
            {
                editBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 10px;");
                deleteBtn.setStyle("-fx-background-color: #F44336; -fx-text-fill: white; -fx-font-size: 10px;");
                
                editBtn.setOnAction(e -> {
                    Student student = getTableView().getItems().get(getIndex());
                    showEditStudentDialog(student);
                });
                
                deleteBtn.setOnAction(e -> {
                    Student student = getTableView().getItems().get(getIndex());
                    deleteStudent(student);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(buttons);
                }
            }
        });
        
        studentsTableView.setItems(filteredStudents);
    }
    
    private void setupFilters() {
        // Setup search functionality
        searchField.textProperty().addListener((obs, oldText, newText) -> filterStudents());
        classFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterStudents());
        majorFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterStudents());
    }
    
    private void loadStudents() {
        try {
            System.out.println("🔄 StudentManagementController: Đang tải danh sách sinh viên...");

            List<User> users = userService.getUsersByRole(User.UserRole.STUDENT);
            System.out.println("📊 StudentManagementController: Tìm thấy " + users.size() + " users với role STUDENT");

            allStudents.clear();

            for (User user : users) {
                if (user instanceof Student) {
                    allStudents.add((Student) user);
                }
            }

            System.out.println("👥 StudentManagementController: Đã tải " + allStudents.size() + " sinh viên");

            updateFilters();
            filterStudents();
            updateTotalLabel();

            System.out.println("✅ StudentManagementController: Tải sinh viên hoàn tất");
        } catch (Exception e) {
            System.err.println("❌ StudentManagementController: Lỗi khi tải sinh viên: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể tải danh sách sinh viên: " + e.getMessage());
        }
    }
    
    private void updateFilters() {
        // Update class filter
        List<String> classes = allStudents.stream()
                .map(Student::getClassName)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        classFilterComboBox.setItems(FXCollections.observableArrayList(classes));
        
        // Update major filter
        List<String> majors = allStudents.stream()
                .map(Student::getMajor)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        majorFilterComboBox.setItems(FXCollections.observableArrayList(majors));
    }
    
    private void filterStudents() {
        String searchText = searchField.getText().toLowerCase();
        String selectedClass = classFilterComboBox.getValue();
        String selectedMajor = majorFilterComboBox.getValue();
        
        List<Student> filtered = allStudents.stream()
                .filter(student -> {
                    boolean matchesSearch = searchText.isEmpty() || 
                            student.getFullName().toLowerCase().contains(searchText) ||
                            student.getStudentId().toLowerCase().contains(searchText) ||
                            student.getEmail().toLowerCase().contains(searchText);
                    
                    boolean matchesClass = selectedClass == null || 
                            student.getClassName().equals(selectedClass);
                    
                    boolean matchesMajor = selectedMajor == null || 
                            student.getMajor().equals(selectedMajor);
                    
                    return matchesSearch && matchesClass && matchesMajor;
                })
                .collect(Collectors.toList());
        
        filteredStudents.setAll(filtered);
        updateTotalLabel();
    }
    
    private void updateTotalLabel() {
        totalStudentsLabel.setText("Tổng số sinh viên: " + filteredStudents.size() + "/" + allStudents.size());
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml", 
                                   "Quản trị viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void showAddStudentDialog() {
        Dialog<Student> dialog = createStudentDialog(null);
        Optional<Student> result = dialog.showAndWait();
        
        result.ifPresent(student -> {
            // Kiểm tra validation trước khi thêm
            if (student.getUsername() == null || student.getUsername().trim().isEmpty()) {
                showAlert("Lỗi", "Tên đăng nhập không được để trống!");
                return;
            }

            if (student.getPassword() == null || student.getPassword().trim().isEmpty()) {
                showAlert("Lỗi", "Mật khẩu không được để trống!");
                return;
            }

            if (student.getFullName() == null || student.getFullName().trim().isEmpty()) {
                showAlert("Lỗi", "Họ tên không được để trống!");
                return;
            }

            // Thêm sinh viên
            if (userService.addUser(student)) {
                showAlert("Thành công", "Thêm sinh viên thành công!\nMSSV: " + student.getStudentId());
                loadStudents();
            } else {
                // Kiểm tra lỗi cụ thể
                if (userService.isUsingDatabaseStorage()) {
                    showAlert("Lỗi", "Không thể thêm sinh viên!\n\nCó thể do:\n• Tên đăng nhập '" + student.getUsername() + "' đã tồn tại\n• MSSV '" + student.getStudentId() + "' đã tồn tại\n• Lỗi kết nối database");
                } else {
                    showAlert("Lỗi", "Tên đăng nhập '" + student.getUsername() + "' đã tồn tại!");
                }
            }
        });
    }
    
    private void showEditStudentDialog(Student student) {
        Dialog<Student> dialog = createStudentDialog(student);
        Optional<Student> result = dialog.showAndWait();
        
        result.ifPresent(updatedStudent -> {
            if (userService.updateUser(updatedStudent)) {
                showAlert("Thành công", "Cập nhật sinh viên thành công!");
                loadStudents();
            } else {
                showAlert("Lỗi", "Không thể cập nhật sinh viên.");
            }
        });
    }
    
    private Dialog<Student> createStudentDialog(Student existingStudent) {
        Dialog<Student> dialog = new Dialog<>();
        dialog.setTitle(existingStudent == null ? "Thêm sinh viên mới" : "Sửa thông tin sinh viên");
        dialog.setHeaderText(null);

        // Create custom buttons with enhanced styling
        ButtonType saveButtonType = new ButtonType("💾 Lưu", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("❌ Hủy", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, cancelButtonType);

        // Create main container with optimized sizing
        VBox mainContainer = new VBox();
        mainContainer.setSpacing(15);
        mainContainer.setPadding(new Insets(20, 30, 15, 30));
        mainContainer.setPrefWidth(580);
        mainContainer.setPrefHeight(580);
        mainContainer.setStyle(
            "-fx-background-color: #FAFAFA; " +
            "-fx-background-radius: 10px;"
        );

        // Create compact header section
        HBox headerSection = new HBox();
        headerSection.setSpacing(10);
        headerSection.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        headerSection.setPadding(new Insets(12, 15, 12, 15));
        headerSection.setStyle(
            "-fx-background-color: linear-gradient(to right, #2196F3, #21CBF3); " +
            "-fx-background-radius: 8px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 6, 0, 0, 2);"
        );

        Label headerIcon = new Label("👨‍🎓");
        headerIcon.setStyle("-fx-font-size: 18px;");

        Label headerTitle = new Label(existingStudent == null ? "THÊM SINH VIÊN MỚI" : "SỬA THÔNG TIN SINH VIÊN");
        headerTitle.setStyle(
            "-fx-font-size: 14px; " +
            "-fx-font-weight: bold; " +
            "-fx-text-fill: white;"
        );

        headerSection.getChildren().addAll(headerIcon, headerTitle);

        // Create form container with compact sections
        VBox formContainer = new VBox();
        formContainer.setSpacing(12);
        
        // Create enhanced form fields
        TextField usernameField = createEnhancedTextField("Nhập tên đăng nhập...");
        TextField passwordField = createEnhancedTextField("Nhập mật khẩu...");
        TextField fullNameField = createEnhancedTextField("Nhập họ và tên...");
        TextField emailField = createEnhancedTextField("Nhập email...");
        TextField phoneField = createEnhancedTextField("Nhập số điện thoại...");
        TextField addressField = createEnhancedTextField("Nhập địa chỉ...");
        ComboBox<String> classNameComboBox = createEnhancedComboBox();
        ComboBox<String> majorComboBox = createEnhancedComboBox();
        ComboBox<Integer> yearComboBox = createEnhancedComboBox();
        DatePicker dobPicker = createEnhancedDatePicker();

        // Setup class options from 24IT1 to 24IT8
        classNameComboBox.setItems(FXCollections.observableArrayList(
            "24IT1", "24IT2", "24IT3", "24IT4", "24IT5", "24IT6", "24IT7", "24IT8"
        ));
        classNameComboBox.setPromptText("Chọn lớp...");

        // Setup major options
        majorComboBox.setItems(FXCollections.observableArrayList(
            "Công nghệ thông tin - Kỹ sư",
            "Công nghệ thông tin - Cử nhân",
            "Công nghệ thông tin (Hợp tác doanh nghiệp) - Cử nhân",
            "Chuyên ngành Công nghệ Game - Kỹ sư",
            "Trí tuệ nhân tạo - Kỹ sư",
            "Chuyên ngành Phân tích dữ liệu - Kỹ sư",
            "Công nghệ kỹ thuật máy tính - Kỹ sư",
            "Công nghệ kỹ thuật máy tính - Cử nhân",
            "Chuyên ngành Thiết kế Vi mạch bán dẫn - Kỹ sư",
            "Chuyên ngành Kỹ thuật phần mềm ô tô - Kỹ sư",
            "An toàn thông tin - Kỹ sư",
            "Công nghệ Truyền thông - Cử nhân",
            "Chuyên ngành Thiết kế Mỹ thuật số - Cử nhân",
            "Quản trị kinh doanh - Cử nhân",
            "Chuyên ngành Quản trị Logistics và Chuỗi cung ứng - Cử nhân",
            "Chuyên ngành Quản trị Dịch vụ du lịch và Lữ hành số - Cử nhân",
            "Chuyên ngành Quản trị Dự án Công nghệ thông tin - Cử nhân",
            "Marketing - Cử nhân",
            "Công nghệ Tài chính - Cử nhân"
        ));
        majorComboBox.setPromptText("Chọn ngành...");

        yearComboBox.setItems(FXCollections.observableArrayList(1, 2, 3, 4, 5));
        yearComboBox.setPromptText("Chọn năm học...");

        dobPicker.setPromptText("Chọn ngày sinh...");

        // Create optimized form grid
        GridPane formGrid = new GridPane();
        formGrid.setHgap(20);
        formGrid.setVgap(15);
        formGrid.setPadding(new Insets(20));
        formGrid.setPrefWidth(520);
        formGrid.setStyle(
            "-fx-background-color: white; " +
            "-fx-background-radius: 8px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 4, 0, 0, 2);"
        );

        // Add form fields in a compact 2-column layout
        int row = 0;
        formGrid.add(createCompactLabel("Tên đăng nhập *"), 0, row);
        formGrid.add(usernameField, 1, row++);

        formGrid.add(createCompactLabel("Mật khẩu *"), 0, row);
        formGrid.add(passwordField, 1, row++);

        formGrid.add(createCompactLabel("Họ và tên *"), 0, row);
        formGrid.add(fullNameField, 1, row++);

        formGrid.add(createCompactLabel("Email"), 0, row);
        formGrid.add(emailField, 1, row++);

        formGrid.add(createCompactLabel("Điện thoại"), 0, row);
        formGrid.add(phoneField, 1, row++);

        formGrid.add(createCompactLabel("Địa chỉ"), 0, row);
        formGrid.add(addressField, 1, row++);

        formGrid.add(createCompactLabel("Lớp *"), 0, row);
        formGrid.add(classNameComboBox, 1, row++);

        formGrid.add(createCompactLabel("Ngành *"), 0, row);
        formGrid.add(majorComboBox, 1, row++);

        formGrid.add(createCompactLabel("Năm học"), 0, row);
        formGrid.add(yearComboBox, 1, row++);

        formGrid.add(createCompactLabel("Ngày sinh"), 0, row);
        formGrid.add(dobPicker, 1, row);
        
        // Load existing data if editing
        if (existingStudent != null) {
            usernameField.setText(existingStudent.getUsername());
            passwordField.setText(existingStudent.getPassword());
            fullNameField.setText(existingStudent.getFullName());
            emailField.setText(existingStudent.getEmail());
            phoneField.setText(existingStudent.getPhone());
            addressField.setText(existingStudent.getAddress());
            classNameComboBox.setValue(existingStudent.getClassName());
            majorComboBox.setValue(existingStudent.getMajor());
            yearComboBox.setValue(existingStudent.getYear());
            dobPicker.setValue(existingStudent.getDateOfBirth());
        }

        // Add form to container
        formContainer.getChildren().add(formGrid);

        // Add all components to main container
        mainContainer.getChildren().addAll(headerSection, formContainer);

        // Create scroll pane to ensure content fits
        ScrollPane scrollPane = new ScrollPane();
        scrollPane.setContent(mainContainer);
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(true);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setStyle("-fx-background-color: transparent;");

        // Set the content
        dialog.getDialogPane().setContent(scrollPane);

        // Set dialog size to ensure buttons are visible
        dialog.getDialogPane().setPrefSize(620, 650);
        dialog.getDialogPane().setMinSize(620, 650);
        dialog.getDialogPane().setMaxSize(620, 650);
        dialog.setResizable(false);

        // Apply enhanced styling to dialog
        dialog.getDialogPane().getStylesheets().add(
            getClass().getResource("/com/example/doancuoikyjava/enhanced-styles.css").toExternalForm()
        );
        dialog.getDialogPane().getStyleClass().add("enhanced-dialog");

        // Style the buttons with better visibility
        javafx.application.Platform.runLater(() -> {
            Button saveBtn = (Button) dialog.getDialogPane().lookupButton(saveButtonType);
            Button cancelBtn = (Button) dialog.getDialogPane().lookupButton(cancelButtonType);

            if (saveBtn != null) {
                saveBtn.setText("💾 Lưu");
                saveBtn.setStyle(
                    "-fx-background-color: #4CAF50; " +
                    "-fx-text-fill: white; " +
                    "-fx-font-weight: bold; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-radius: 8px; " +
                    "-fx-padding: 12px 25px; " +
                    "-fx-cursor: hand; " +
                    "-fx-min-width: 100px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);"
                );
            }

            if (cancelBtn != null) {
                cancelBtn.setText("❌ Hủy");
                cancelBtn.setStyle(
                    "-fx-background-color: #F44336; " +
                    "-fx-text-fill: white; " +
                    "-fx-font-weight: bold; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-radius: 8px; " +
                    "-fx-padding: 12px 25px; " +
                    "-fx-cursor: hand; " +
                    "-fx-min-width: 100px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);"
                );
            }
        });
        
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == saveButtonType) {
                try {
                    Student student = existingStudent != null ? existingStudent : new Student();
                    
                    if (existingStudent == null) {
                        String studentId = userService.generateNextUserId(User.UserRole.STUDENT);
                        student.setUserId(studentId);
                        student.setStudentId(studentId);
                    }
                    
                    student.setUsername(usernameField.getText());
                    student.setPassword(passwordField.getText());
                    student.setFullName(fullNameField.getText());
                    student.setEmail(emailField.getText());
                    student.setPhone(phoneField.getText());
                    student.setAddress(addressField.getText());
                    student.setClassName(classNameComboBox.getValue());
                    student.setMajor(majorComboBox.getValue());
                    student.setYear(yearComboBox.getValue());
                    student.setDateOfBirth(dobPicker.getValue());
                    
                    return student;
                } catch (Exception e) {
                    showAlert("Lỗi", "Dữ liệu không hợp lệ: " + e.getMessage());
                    return null;
                }
            }
            return null;
        });
        
        return dialog;
    }
    
    private void deleteStudent(Student student) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận xóa");
        alert.setHeaderText("Bạn có chắc chắn muốn xóa sinh viên này?");
        alert.setContentText("Sinh viên: " + student.getFullName() + " (" + student.getStudentId() + ")");
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.get() == ButtonType.OK) {
            if (userService.deleteUser(student.getUserId())) {
                showAlert("Thành công", "Xóa sinh viên thành công!");
                loadStudents();
            } else {
                showAlert("Lỗi", "Không thể xóa sinh viên.");
            }
        }
    }
    
    @FXML
    private void deleteSelectedStudents() {
        List<Student> selectedStudents = studentsTableView.getSelectionModel().getSelectedItems();
        
        if (selectedStudents.isEmpty()) {
            showAlert("Thông báo", "Vui lòng chọn sinh viên cần xóa.");
            return;
        }
        
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận xóa");
        alert.setHeaderText("Bạn có chắc chắn muốn xóa " + selectedStudents.size() + " sinh viên đã chọn?");
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.get() == ButtonType.OK) {
            int deletedCount = 0;
            for (Student student : selectedStudents) {
                if (userService.deleteUser(student.getUserId())) {
                    deletedCount++;
                }
            }
            
            showAlert("Thành công", "Đã xóa " + deletedCount + "/" + selectedStudents.size() + " sinh viên.");
            loadStudents();
        }
    }
    
    @FXML
    private void refreshData() {
        loadStudents();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchStudents() {
        filterStudents();
    }

    @FXML
    private void exportData() {
        showAlert("Thông báo", "Tính năng xuất Excel đang được phát triển!");
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // Helper methods for enhanced UI components
    private TextField createEnhancedTextField(String promptText) {
        TextField textField = new TextField();
        textField.setPromptText(promptText);
        textField.setPrefWidth(320);
        textField.setStyle(
            "-fx-background-radius: 6px; " +
            "-fx-border-radius: 6px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 1px; " +
            "-fx-padding: 12px 15px; " +
            "-fx-font-size: 14px; " +
            "-fx-background-color: white; " +
            "-fx-text-fill: #212121; " +
            "-fx-prompt-text-fill: #757575; " +
            "-fx-pref-height: 40px;"
        );

        // Add focus effects
        textField.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                textField.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #2196F3; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 12px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-prompt-text-fill: #757575; " +
                    "-fx-pref-height: 40px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.2), 3, 0, 0, 0);"
                );
            } else {
                textField.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #E0E0E0; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 12px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-prompt-text-fill: #757575; " +
                    "-fx-pref-height: 40px;"
                );
            }
        });

        return textField;
    }

    private Label createCompactLabel(String text) {
        Label label = new Label(text);
        label.setStyle(
            "-fx-font-size: 15px; " +
            "-fx-font-weight: bold; " +
            "-fx-text-fill: #424242; " +
            "-fx-min-width: 140px; " +
            "-fx-pref-width: 140px;"
        );
        // Ensure text is visible
        label.setTextFill(javafx.scene.paint.Color.web("#424242"));
        return label;
    }

    private <T> ComboBox<T> createEnhancedComboBox() {
        ComboBox<T> comboBox = new ComboBox<>();
        comboBox.setPrefWidth(320);
        comboBox.setStyle(
            "-fx-background-radius: 6px; " +
            "-fx-border-radius: 6px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 1px; " +
            "-fx-padding: 10px 15px; " +
            "-fx-font-size: 14px; " +
            "-fx-background-color: white; " +
            "-fx-text-fill: #212121; " +
            "-fx-pref-height: 40px;"
        );

        comboBox.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                comboBox.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #2196F3; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 10px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-pref-height: 40px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.2), 3, 0, 0, 0);"
                );
            } else {
                comboBox.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #E0E0E0; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 10px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-pref-height: 40px;"
                );
            }
        });

        return comboBox;
    }

    private DatePicker createEnhancedDatePicker() {
        DatePicker datePicker = new DatePicker();
        datePicker.setPrefWidth(320);
        datePicker.setStyle(
            "-fx-background-radius: 6px; " +
            "-fx-border-radius: 6px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 1px; " +
            "-fx-padding: 10px 15px; " +
            "-fx-font-size: 14px; " +
            "-fx-background-color: white; " +
            "-fx-text-fill: #212121; " +
            "-fx-pref-height: 40px;"
        );

        datePicker.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                datePicker.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #2196F3; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 10px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-pref-height: 40px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.2), 3, 0, 0, 0);"
                );
            } else {
                datePicker.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #E0E0E0; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 10px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-pref-height: 40px;"
                );
            }
        });

        return datePicker;
    }

    private VBox createFormSection(String title, HBox... fieldRows) {
        VBox section = new VBox();
        section.setSpacing(15);
        section.setPadding(new Insets(20));
        section.setStyle(
            "-fx-background-color: white; " +
            "-fx-background-radius: 10px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 6, 0, 0, 3);"
        );

        Label sectionTitle = new Label(title);
        sectionTitle.setStyle(
            "-fx-font-size: 16px; " +
            "-fx-font-weight: bold; " +
            "-fx-text-fill: #2196F3; " +
            "-fx-padding: 0 0 10 0;"
        );

        section.getChildren().add(sectionTitle);
        section.getChildren().addAll(fieldRows);

        return section;
    }

    private HBox createFieldRow(String labelText, javafx.scene.Node field) {
        HBox row = new HBox();
        row.setSpacing(15);
        row.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        Label label = new Label(labelText);
        label.setStyle(
            "-fx-font-size: 14px; " +
            "-fx-font-weight: bold; " +
            "-fx-text-fill: #424242; " +
            "-fx-min-width: 120px;"
        );

        field.setStyle(field.getStyle() + "-fx-pref-width: 300px;");

        row.getChildren().addAll(label, field);
        return row;
    }
}
